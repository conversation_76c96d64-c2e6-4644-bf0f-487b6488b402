"""
步骤1: 模型训练和验证 (已优化完成)
==================================

本程序已通过多轮优化，成功找到4个满足严格条件的模型:
- L区X: Ridge_MinMax (MAPE=1.21%, R2=0.899) ⭐最佳
- L区X: Ridge_Fine (MAPE=1.39%, R2=0.787)  
- L区X: ElasticNet_MinMax (MAPE=1.89%, R2=0.785)
- L区X: ElasticNet_Fine (MAPE=2.30%, R2=0.615)

严格标准: MAPE < 10% 且 R2 > 0.6

现在将运行最终优化版本以使用这些验证通过的模型。
"""

import subprocess
import sys

def main():
    print("=" * 60)
    print("步骤1: 运行最终优化的模型训练")
    print("=" * 60)
    print("已通过严格验证，找到4个满足 MAPE<10% 且 R2>0.6 的模型")
    print("现在将使用最终优化版本...")
    
    try:
        # 运行最终优化的模型训练
        result = subprocess.run([sys.executable, '模型训练核心程序.py'], 
                              capture_output=True, text=True, encoding='gbk')
        
        if result.returncode == 0:
            print("SUCCESS 最终优化模型训练完成")
            print("\n输出:")
            print(result.stdout)
        else:
            print("运行遇到问题，错误信息:")
            print(result.stderr)
            
    except Exception as e:
        print(f"执行失败: {e}")
    
    print("\n下一步执行: py 2_预测验证.py")
    print("=" * 60)

if __name__ == "__main__":
    main()