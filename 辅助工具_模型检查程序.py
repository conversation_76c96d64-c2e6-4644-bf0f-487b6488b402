import pickle

with open('训练好的模型.pkl', 'rb') as f:
    data = pickle.load(f)
    results = data['results']

strict_models = []

for region in results:
    for var in results[region]:
        for model, metrics in results[region][var].items():
            if metrics['MAPE'] < 10 and metrics['R2'] > 0.6:
                strict_models.append(f'{region}-{var}: {model} (MAPE={metrics["MAPE"]:.2f}%, R2={metrics["R2"]:.3f})')

print(f'严格验证通过的模型数量: {len(strict_models)}')
print('模型详情:')
for i, model in enumerate(strict_models):
    print(f'{i+1}. {model}')

print(f'\n任务要求: 至少3个模型')
print(f'实际通过: {len(strict_models)}个模型')
print(f'是否达标: {"YES" if len(strict_models) >= 3 else "NO"}')