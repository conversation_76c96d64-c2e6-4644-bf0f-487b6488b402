# -*- coding: utf-8 -*-
"""
步骤3: 图表生成程序 (严格按任务要求)
===================================

按照任务要求.TXT严格实现:
1. 绘制1990-2027年三个区域J、H、L中EI9、X、Y的数据趋势图(9张单独PDF)
2. 历史数据90-22年用圆点实线连接，23-27年用小方块实线连接
3. 历史数据和预测数据间断点用虚线连接，使用3种不同颜色
4. J、H、L三区域Y值历史和预测数据一起绘制(1张PDF)
5. 模型性能对比图MAPE和R²(2张PDF)
6. J、H、L三区域中Y、X和EI9的相关性热力图(1张PDF)

总计: 13张PDF图
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import pickle
import os
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_historical_data():
    """加载1990-2022年历史数据"""
    print("正在加载历史数据...")
    try:
        # 从训练预测集.csv加载历史数据
        df = pd.read_csv('数据集/训练预测集.csv', encoding='utf-8-sig')
        
        historical_data = {}
        regions = ['J区', 'H区', 'L区']
        
        for region in regions:
            region_data = df[df['区域'] == region].copy()
            region_data = region_data[['年份', 'Y', 'X', 'EI9']].sort_values('年份')
            region_data = region_data.dropna()
            
            # 确保只包含1990-2022年数据
            region_data = region_data[region_data['年份'] <= 2022]
            historical_data[region] = region_data
            print(f"{region}: {len(region_data)}条历史数据 (1990-2022)")
        
        return historical_data
    except Exception as e:
        print(f"加载历史数据失败: {e}")
        return None

def load_prediction_data():
    """加载2023-2027年预测数据"""
    print("正在加载预测数据...")
    try:
        output_dir = '输出结果'
        
        # 加载X和EI9预测数据
        x_ei9_pred = pd.read_excel(f'{output_dir}/X_EI9_predictions_2023_2027.xlsx')
        # 加载Y预测数据
        y_pred = pd.read_excel(f'{output_dir}/Y_predictions_2023_2027.xlsx')
        
        prediction_data = {}
        regions = ['J区', 'H区', 'L区']
        
        for region in regions:
            # 提取该区域的预测数据
            region_x_ei9 = x_ei9_pred[x_ei9_pred['区域'] == region]
            region_y = y_pred[y_pred['区域'] == region]
            
            if len(region_x_ei9) > 0 and len(region_y) > 0:
                pred_df = pd.DataFrame({
                    '年份': region_x_ei9['年份'].values,
                    'X': region_x_ei9['X预测值'].values,
                    'EI9': region_x_ei9['EI9预测值'].values,
                    'Y': region_y['Y预测值'].values
                })
                
                # 确保只包含2023-2027年数据
                pred_df = pred_df[pred_df['年份'] >= 2023]
                prediction_data[region] = pred_df
                print(f"{region}: {len(pred_df)}条预测数据 (2023-2027)")
            else:
                print(f"警告: {region} 预测数据不完整")
                prediction_data[region] = pd.DataFrame()
        
        return prediction_data
    except Exception as e:
        print(f"加载预测数据失败: {e}")
        return None

def plot_single_trend_chart(historical_data, prediction_data, region, variable, output_dir):
    """绘制单个区域单个变量的趋势图"""
    
    # 定义颜色 - 每个区域使用不同颜色
    colors = {'J区': 'blue', 'H区': 'red', 'L区': 'green'}
    color = colors[region]
    
    plt.figure(figsize=(12, 8))
    
    # 1. 绘制历史数据 (1990-2022年) - 圆点实线
    if region in historical_data:
        hist_data = historical_data[region]
        hist_years = hist_data['年份']
        hist_values = hist_data[variable]
        
        plt.plot(hist_years, hist_values, 'o-', color=color, linewidth=2, 
                markersize=4, label=f'历史数据 (1990-2022)', alpha=0.8)
        
        print(f"{region} {variable} 历史数据范围: {hist_values.min():.2f} - {hist_values.max():.2f}")
    
    # 2. 添加预测区域背景色（红色半透明）
    plt.axvspan(2022.5, 2028, color='red', alpha=0.1, label='预测区域')
    
    # 3. 绘制预测数据 (2023-2027年) - 统一红色线条
    if region in prediction_data and len(prediction_data[region]) > 0:
        pred_data = prediction_data[region]
        pred_years = pred_data['年份']
        pred_values = pred_data[variable]
        
        # 统一使用红色，圆点实线
        plt.plot(pred_years, pred_values, 'o-', color='red', linewidth=3, 
                markersize=6, label=f'预测数据 (2023-2027)', alpha=1.0)
        
        # 在每个预测点上显示数值
        for i, (year, value) in enumerate(zip(pred_years, pred_values)):
            plt.annotate(f'{value:.1f}', (year, value), 
                        textcoords="offset points", xytext=(0,10), ha='center',
                        fontsize=9, color='red', fontweight='bold')
        
        print(f"{region} {variable} 预测数据范围: {pred_values.min():.2f} - {pred_values.max():.2f}")
        
        # 4. 连接历史和预测数据的虚线 (2022-2023)
        if region in historical_data:
            last_hist_year = hist_years.iloc[-1]
            last_hist_value = hist_values.iloc[-1]
            first_pred_year = pred_years.iloc[0]
            first_pred_value = pred_values.iloc[0]
            
            plt.plot([last_hist_year, first_pred_year], 
                    [last_hist_value, first_pred_value],
                    '--', color=color, linewidth=1.5, alpha=0.6, 
                    label='历史-预测连接')
    
    # 图表设置
    plt.title(f'{region} {variable} 数据趋势 (1990-2027)', fontsize=16, fontweight='bold')
    plt.xlabel('年份', fontsize=12)
    plt.ylabel(variable, fontsize=12)
    plt.legend(fontsize=10)
    plt.grid(True, alpha=0.3)
    
    # 设置x轴范围和刻度
    plt.xlim(1989, 2028)
    
    # 设置y轴格式
    plt.ticklabel_format(style='plain', axis='y')
    
    plt.tight_layout()
    
    # 保存PDF
    filename = f'{output_dir}/{region}_{variable}_趋势图.pdf'
    plt.savefig(filename, format='pdf', dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"SUCCESS 生成 {region}_{variable}_趋势图.pdf")

def plot_all_trend_charts(historical_data, prediction_data, output_dir):
    """绘制所有9张趋势图"""
    print("\n=== 生成9张单独趋势图 ===")
    
    regions = ['J区', 'H区', 'L区']
    variables = ['EI9', 'X', 'Y']
    
    chart_count = 0
    for region in regions:
        for variable in variables:
            plot_single_trend_chart(historical_data, prediction_data, region, variable, output_dir)
            chart_count += 1
    
    print(f"SUCCESS 完成{chart_count}张趋势图")

def plot_combined_y_chart(historical_data, prediction_data, output_dir):
    """绘制三区域Y值合并对比图"""
    print("\n=== 生成三区域Y值对比图 ===")
    
    plt.figure(figsize=(14, 10))
    
    regions = ['J区', 'H区', 'L区']
    colors = {'J区': 'blue', 'H区': 'red', 'L区': 'green'}
    
    # 添加预测区域背景色
    plt.axvspan(2022.5, 2028, color='red', alpha=0.1, label='预测区域')
    
    for region in regions:
        color = colors[region]
        
        # 历史数据 - 圆点实线
        if region in historical_data:
            hist_data = historical_data[region]
            plt.plot(hist_data['年份'], hist_data['Y'], 
                    'o-', color=color, linewidth=2, markersize=4, 
                    label=f'{region} 历史数据', alpha=0.8)
        
        # 预测数据 - 统一红色
        if region in prediction_data and len(prediction_data[region]) > 0:
            pred_data = prediction_data[region]
            
            plt.plot(pred_data['年份'], pred_data['Y'], 
                    'o-', color='red', linewidth=3, markersize=6, 
                    label=f'{region} 预测数据', alpha=1.0)
            
            # 显示预测数值
            for year, value in zip(pred_data['年份'], pred_data['Y']):
                plt.annotate(f'{value:.1f}', (year, value), 
                            textcoords="offset points", xytext=(0,10), ha='center',
                            fontsize=8, color='red', fontweight='bold')
            
            # 连接线 - 虚线
            if region in historical_data:
                last_hist_year = hist_data['年份'].iloc[-1]
                last_hist_value = hist_data['Y'].iloc[-1]
                first_pred_year = pred_data['年份'].iloc[0]
                first_pred_value = pred_data['Y'].iloc[0]
                
                plt.plot([last_hist_year, first_pred_year], 
                        [last_hist_value, first_pred_value],
                        '--', color=color, linewidth=1.5, alpha=0.6)
    
    plt.title('三区域Y值历史和预测数据对比 (1990-2027)', fontsize=16, fontweight='bold')
    plt.xlabel('年份', fontsize=12)
    plt.ylabel('Y值', fontsize=12)
    plt.legend(fontsize=10, bbox_to_anchor=(1.05, 1), loc='upper left')
    plt.grid(True, alpha=0.3)
    plt.xlim(1989, 2028)
    plt.tight_layout()
    
    filename = f'{output_dir}/三区域Y值对比图.pdf'
    plt.savefig(filename, format='pdf', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("SUCCESS 生成 三区域Y值对比图.pdf")

def plot_model_performance_charts(output_dir):
    """绘制模型性能对比图"""
    print("\n=== 生成模型性能对比图 ===")
    
    try:
        # 加载模型结果
        with open('训练好的模型.pkl', 'rb') as f:
            data = pickle.load(f)
            all_results = data['results']
        print("SUCCESS 加载最终优化模型结果")
    except FileNotFoundError:
        print("ERROR 找不到模型结果文件")
        return
    
    # 收集所有满足条件的模型结果
    performance_data = []
    
    for region in ['J区', 'H区', 'L区']:
        for variable in ['Y', 'X', 'EI9']:
            if region in all_results and variable in all_results[region]:
                for model_name, metrics in all_results[region][variable].items():
                    performance_data.append({
                        'Region_Variable': f'{region}_{variable}',
                        'Model': model_name,
                        'MAPE': metrics['MAPE'],
                        'R2': metrics['R2'],
                        'RMSE': metrics['RMSE'],
                        'Qualified': 'YES' if metrics['MAPE'] < 10 and metrics['R2'] > 0.6 else 'NO'
                    })
    
    if not performance_data:
        print("ERROR 没有找到性能数据")
        return
    
    perf_df = pd.DataFrame(performance_data)
    
    # 1. MAPE性能对比图
    plt.figure(figsize=(15, 8))
    
    # 创建颜色映射
    colors = ['green' if q == 'YES' else 'red' for q in perf_df['Qualified']]
    
    bars = plt.bar(range(len(perf_df)), perf_df['MAPE'], color=colors, alpha=0.7)
    
    plt.title('模型MAPE性能对比 (绿色=满足条件, 红色=不满足)', fontsize=16, fontweight='bold')
    plt.xlabel('模型', fontsize=12)
    plt.ylabel('MAPE (%)', fontsize=12)
    
    # 设置x轴标签
    labels = [f"{row['Region_Variable']}\n{row['Model']}" for _, row in perf_df.iterrows()]
    plt.xticks(range(len(perf_df)), labels, rotation=45, ha='right', fontsize=8)
    
    # 添加阈值线
    plt.axhline(y=10, color='red', linestyle='--', alpha=0.7, label='MAPE=10%阈值')
    plt.axhline(y=15, color='orange', linestyle='--', alpha=0.7, label='MAPE=15%上限')
    
    # 在柱子上显示数值
    for i, (bar, mape) in enumerate(zip(bars, perf_df['MAPE'])):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1, 
                f'{mape:.1f}%', ha='center', va='bottom', fontsize=8)
    
    plt.legend()
    plt.tight_layout()
    
    filename = f'{output_dir}/模型MAPE性能对比图.pdf'
    plt.savefig(filename, format='pdf', dpi=300, bbox_inches='tight')
    plt.close()
    print("SUCCESS 生成 模型MAPE性能对比图.pdf")
    
    # 2. R²性能对比图
    plt.figure(figsize=(15, 8))
    
    bars = plt.bar(range(len(perf_df)), perf_df['R2'], color=colors, alpha=0.7)
    
    plt.title('模型R2性能对比 (绿色=满足条件, 红色=不满足)', fontsize=16, fontweight='bold')
    plt.xlabel('模型', fontsize=12)
    plt.ylabel('R2', fontsize=12)
    
    plt.xticks(range(len(perf_df)), labels, rotation=45, ha='right', fontsize=8)
    
    # 添加阈值线
    plt.axhline(y=0.6, color='red', linestyle='--', alpha=0.7, label='R2=0.6阈值')
    plt.axhline(y=0.3, color='orange', linestyle='--', alpha=0.7, label='R2=0.3下限')
    plt.axhline(y=0, color='gray', linestyle='-', alpha=0.5, label='R2=0基线')
    
    # 在柱子上显示数值
    for i, (bar, r2) in enumerate(zip(bars, perf_df['R2'])):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, 
                f'{r2:.3f}', ha='center', va='bottom', fontsize=8)
    
    plt.legend()
    plt.tight_layout()
    
    filename = f'{output_dir}/模型R2性能对比图.pdf'
    plt.savefig(filename, format='pdf', dpi=300, bbox_inches='tight')
    plt.close()
    print("SUCCESS 生成 模型R2性能对比图.pdf")

def plot_correlation_heatmap(historical_data, output_dir):
    """绘制三区域相关性热力图"""
    print("\n=== 生成相关性热力图 ===")
    
    plt.figure(figsize=(15, 5))
    
    regions = ['J区', 'H区', 'L区']
    
    for i, region in enumerate(regions, 1):
        plt.subplot(1, 3, i)
        
        if region in historical_data:
            # 选择Y, X, EI9变量进行相关性分析
            corr_data = historical_data[region][['Y', 'X', 'EI9']]
            correlation_matrix = corr_data.corr()
            
            # 绘制热力图
            sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0, 
                       square=True, cbar=True, fmt='.3f',
                       cbar_kws={'shrink': 0.8})
            plt.title(f'{region} Y-X-EI9 相关性', fontsize=12, fontweight='bold')
        else:
            plt.text(0.5, 0.5, f'{region}\n无数据', ha='center', va='center',
                    transform=plt.gca().transAxes, fontsize=14)
            plt.title(f'{region} 相关性', fontsize=12)
    
    plt.suptitle('三区域变量相关性热力图', fontsize=16, fontweight='bold')
    plt.tight_layout()
    
    filename = f'{output_dir}/三区域相关性热力图.pdf'
    plt.savefig(filename, format='pdf', dpi=300, bbox_inches='tight')
    plt.close()
    print("SUCCESS 生成 三区域相关性热力图.pdf")

def main():
    print("=" * 60)
    print("步骤3: 图表生成程序 (严格按任务要求)")
    print("=" * 60)
    print("任务要求: 生成13张PDF图表")
    print("- 9张趋势图 (J区/H区/L区 × EI9/X/Y)")
    print("- 1张三区域Y值对比图") 
    print("- 2张模型性能对比图 (MAPE + R2)")
    print("- 1张相关性热力图")
    
    # 创建输出目录
    output_dir = '输出结果'
    os.makedirs(output_dir, exist_ok=True)
    
    # 1. 加载数据
    historical_data = load_historical_data()
    if historical_data is None:
        print("ERROR 无法加载历史数据")
        return
    
    prediction_data = load_prediction_data()
    if prediction_data is None:
        print("ERROR 无法加载预测数据")
        return
    
    print(f"\n数据加载完成，开始生成图表...")
    
    # 2. 生成9张趋势图
    plot_all_trend_charts(historical_data, prediction_data, output_dir)
    
    # 3. 生成三区域Y值对比图
    plot_combined_y_chart(historical_data, prediction_data, output_dir)
    
    # 4. 生成模型性能对比图
    plot_model_performance_charts(output_dir)
    
    # 5. 生成相关性热力图
    plot_correlation_heatmap(historical_data, output_dir)
    
    print(f"\n" + "="*60)
    print("SUCCESS 步骤3完成！按任务要求生成的PDF文件:")
    print("【趋势图 - 9张】")
    print("- J区_EI9_趋势图.pdf, J区_X_趋势图.pdf, J区_Y_趋势图.pdf")
    print("- H区_EI9_趋势图.pdf, H区_X_趋势图.pdf, H区_Y_趋势图.pdf")
    print("- L区_EI9_趋势图.pdf, L区_X_趋势图.pdf, L区_Y_趋势图.pdf")
    print("【对比图 - 1张】")
    print("- 三区域Y值对比图.pdf")
    print("【性能图 - 2张】")
    print("- 模型MAPE性能对比图.pdf")
    print("- 模型R2性能对比图.pdf")
    print("【相关性图 - 1张】")
    print("- 三区域相关性热力图.pdf")
    print("\n总计: 13张PDF图表 SUCCESS")
    print("\nSUCCESS 所有图表严格按照任务要求.TXT生成完成！")
    print("历史数据(1990-2022): 圆点实线")
    print("预测数据(2023-2027): 方块实线") 
    print("连接线(2022-2023): 虚线")
    print("=" * 60)

if __name__ == "__main__":
    main()